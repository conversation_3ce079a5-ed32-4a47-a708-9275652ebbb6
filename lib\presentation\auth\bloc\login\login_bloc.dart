import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wesell/core/utils/preferences.dart';
import '../../../../core/services/token_manager.dart';
import '../../../../core/services/auto_refresh_service.dart';
import '../../../../core/services/auto_logout_service.dart';
import '../../../../domain/usecases/login_usecase.dart';
import '../../../../domain/usecases/logout_usecase.dart';
import '../../../../domain/usecases/forgot_password_usecase.dart';
import 'login_event.dart';
import 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final LoginUseCase loginUseCase;
  final LogoutUseCase logoutUseCase;
  final ForgotPasswordUseCase forgotPasswordUseCase;
  final TokenManager tokenManager;
  final AutoRefreshService autoRefreshService;
  final AutoLogoutService autoLogoutService;

  LoginBloc({
    required this.loginUseCase,
    required this.logoutUseCase,
    required this.forgotPasswordUseCase,
    required this.tokenManager,
    required this.autoRefreshService,
    required this.autoLogoutService,
  }) : super(const LoginInitial()) {
    on<LoginSubmitted>(_onLoginSubmitted);
    on<LogoutRequested>(_onLogoutRequested);
    on<CheckLoginStatus>(_onCheckLoginStatus);
    on<ForgotPasswordSubmitted>(_onForgotPasswordSubmitted);

    // Set up token manager callbacks
    _setupTokenManagerCallbacks();
  }

  Future<void> _onLoginSubmitted(
    LoginSubmitted event,
    Emitter<LoginState> emit,
  ) async {
    emit(const LoginLoading());

    final result = await loginUseCase(
      LoginParams(
        email: event.email,
        password: event.password,
      ),
    );

    await result.fold(
      (failure) async => emit(LoginFailure(message: failure.message)),
      (response) async {
        // Save tokens
        await Preferences.saveTokens(
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
        );
        // Decode user from access token and save
        await Preferences.saveUserFromToken(response.accessToken);
        final user = await Preferences.getUser();

        if (!emit.isDone && user != null) {
          // Initialize token management services
          await _initializeTokenServices();
          emit(LoginSuccess(user: user));
        }
      },
    );
  }

  Future<void> _onLogoutRequested(
    LogoutRequested event,
    Emitter<LoginState> emit,
  ) async {
    final result = await logoutUseCase();

    await result.fold(
      (failure) async => emit(LogoutFailure(message: failure.message)),
      (_) async {
        if (!emit.isDone) {
          emit(const LogoutSuccess());
        }
      },
    );
  }

  Future<void> _onCheckLoginStatus(
    CheckLoginStatus event,
    Emitter<LoginState> emit,
  ) async {
    // This would typically check if user is already logged in
    // For now, we'll emit initial state
    emit(const LoginStatusChecked(isLoggedIn: false));
  }

  Future<void> _onForgotPasswordSubmitted(
    ForgotPasswordSubmitted event,
    Emitter<LoginState> emit,
  ) async {
    emit(const ForgotPasswordLoading());

    final result = await forgotPasswordUseCase(
      ForgotPasswordParams(email: event.email),
    );

    result.fold(
      (failure) => emit(ForgotPasswordFailure(message: failure.message)),
      (message) => emit(ForgotPasswordSuccess(message: message)),
    );
  }

  /// Set up token manager callbacks
  void _setupTokenManagerCallbacks() {
    tokenManager.onTokenRefreshed = () {
      // Token was refreshed successfully
      // You can emit a state or perform other actions if needed
    };

    tokenManager.onAutoLogout = () {
      // Auto logout occurred (due to inactivity or token refresh failure)
      add(LogoutRequested());
    };

    tokenManager.onTokenRefreshError = (error) {
      // Token refresh failed
      add(LogoutRequested());
    };
  }

  /// Initialize token management services after successful login
  Future<void> _initializeTokenServices() async {
    // Initialize token manager
    await tokenManager.initialize();

    // Start auto refresh service
    await autoRefreshService.start();

    // Start auto logout service
    autoLogoutService.start();
  }

  /// Clean up token services on logout
  Future<void> _cleanupTokenServices() async {
    // Stop auto refresh service
    autoRefreshService.stop();

    // Stop auto logout service
    autoLogoutService.stop();

    // Clean up token manager
    await tokenManager.cleanup();
  }

  @override
  Future<void> close() {
    // Clean up services when bloc is disposed
    _cleanupTokenServices();
    return super.close();
  }
}
