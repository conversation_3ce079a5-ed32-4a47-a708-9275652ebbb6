import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../network/dio_client.dart';
import '../services/token_manager.dart';
import '../services/auto_refresh_service.dart';
import '../services/auto_logout_service.dart';
import '../../data/datasources/auth_remote_data_source.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/register_usecase.dart';
import '../../domain/usecases/forgot_password_usecase.dart';
import '../../domain/usecases/upload_file_usecase.dart';
import '../../domain/repositories/media_repository.dart';
import '../../data/repositories/media_repository_impl.dart';
import '../../data/datasources/media_remote_data_source.dart';
import '../../presentation/auth/bloc/login/login_bloc.dart';
import '../../presentation/auth/bloc/signup/signup_bloc.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  
  // Core
  sl.registerLazySingleton(() {
    final dioClient = DioClient();
    // Set token manager after both are registered
    return dioClient;
  });
  
  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(dioClient: sl()),
  );
  sl.registerLazySingleton<MediaRemoteDataSource>(
    () => MediaRemoteDataSourceImpl(dioClient: sl()),
  );
  
  // Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: sl(),
      sharedPreferences: sl(),
    ),
  );
  sl.registerLazySingleton<MediaRepository>(
    () => MediaRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => LoginUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  sl.registerLazySingleton(() => RegisterUseCase(sl()));
  sl.registerLazySingleton(() => ForgotPasswordUseCase(sl()));
  sl.registerLazySingleton(() => UploadFileUseCase(sl()));

  // Services
  sl.registerLazySingleton(() => TokenManager(
    authDataSource: sl(),
  ));

  sl.registerLazySingleton(() => AutoRefreshService(
    tokenManager: sl(),
  ));

  sl.registerFactory(() => AutoLogoutService(
    onAutoLogout: () {
      // This will be handled by the LoginBloc
      // The callback will be set up in the LoginBloc constructor
    },
  ));
  
  // Blocs
  sl.registerFactory(
    () => LoginBloc(
      loginUseCase: sl(),
      logoutUseCase: sl(),
      forgotPasswordUseCase: sl(),
      tokenManager: sl(),
      autoRefreshService: sl(),
      autoLogoutService: sl(),
    ),
  );
  sl.registerFactory(
    () => SignupBloc(
      registerUseCase: sl(),
      uploadFileUseCase: sl(),
    ),
  );
}
