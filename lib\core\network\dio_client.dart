import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../error/exceptions.dart';
import '../utils/preferences.dart';
import '../services/token_manager.dart';

class DioClient {
  late final Dio _dio;
  TokenManager? _tokenManager;

  DioClient({TokenManager? tokenManager}) {
    _tokenManager = tokenManager;
    _dio = Dio();
    _setupInterceptors();
  }

  /// Set token manager after initialization
  void setTokenManager(TokenManager tokenManager) {
    _tokenManager = tokenManager;
  }

  void _setupInterceptors() {
    _dio.options = BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: const Duration(milliseconds: AppConstants.connectionTimeout),
      receiveTimeout: const Duration(milliseconds: AppConstants.receiveTimeout),
      headers: {
        AppConstants.contentTypeHeader: AppConstants.applicationJson,
        AppConstants.acceptHeader: AppConstants.applicationJson,
      },
    );

    // Request interceptor
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add authentication token if available
          final token = await Preferences.getAccessToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }

          // Update activity if token manager is available
          _tokenManager?.updateActivity();

          // Log request for debugging
          debugPrint('REQUEST[${options.method}] => PATH: ${options.path}');
          debugPrint('REQUEST DATA: ${options.data}');
          handler.next(options);
        },
        onResponse: (response, handler) {
          debugPrint('RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}');
          debugPrint('RESPONSE DATA: ${response.data}');
          handler.next(response);
        },
        onError: (error, handler) async {
          debugPrint('ERROR[${error.response?.statusCode}] => PATH: ${error.requestOptions.path}');
          debugPrint('ERROR MESSAGE: ${error.message}');

          // Handle 401 Unauthorized - attempt token refresh
          if (error.response?.statusCode == 401 && _tokenManager != null) {
            try {
              final refreshSuccess = await _tokenManager!.refreshToken();
              if (refreshSuccess) {
                // Retry the original request with new token
                final newToken = await Preferences.getAccessToken();
                if (newToken != null) {
                  error.requestOptions.headers['Authorization'] = 'Bearer $newToken';
                  final retryResponse = await _dio.fetch(error.requestOptions);
                  handler.resolve(retryResponse);
                  return;
                }
              }
            } catch (e) {
              debugPrint('Token refresh failed: $e');
            }
          }

          handler.next(error);
        },
      ),
    );
  }

  // GET request
  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
      );
      return response;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // POST request
  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return response;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // PUT request
  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return response;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // DELETE request
  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return response;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Exception _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const TimeoutException(AppConstants.requestTimeout);
      
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['message'] ?? AppConstants.serverError;
        
        switch (statusCode) {
          case 400:
            return ServerException(message: message, statusCode: statusCode);
          case 401:
            return ServerException(message: message, statusCode: statusCode);
          case 409:
            return ServerException(message: message, statusCode: statusCode);
          case 403:
            return const AuthorizationException(AppConstants.authorizationError);
          case 404:
            return const ServerException(message: AppConstants.resourceNotFound);
          case 500:
          case 502:
          case 503:
            return ServerException(message: message, statusCode: statusCode);
          default:
            return ServerException(message: message, statusCode: statusCode);
        }
      
      case DioExceptionType.connectionError:
        return const NetworkException(AppConstants.networkErrorMessage);

      case DioExceptionType.cancel:
        return const UnknownException(AppConstants.unknownErrorMessage);

      case DioExceptionType.unknown:
      default:
        return UnknownException(error.message ?? AppConstants.unknownErrorMessage);
    }
  }
}
