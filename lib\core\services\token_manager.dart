import 'dart:async';
// import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:wesell/core/utils/jwt_decoder.dart';
import '../utils/preferences.dart';
import '../../data/datasources/auth_remote_data_source.dart';

class TokenManager {
  final AuthRemoteDataSource _authDataSource;

  Timer? _refreshTimer;
  
  // Constants
  static const int _refreshBufferMinutes = 5; // Refresh 5 minutes before expiry
  
  // Callbacks
  Function()? onTokenRefreshed;
  Function()? onAutoLogout;
  Function(String error)? onTokenRefreshError;

  TokenManager({
    required AuthRemoteDataSource authDataSource,
  }) : _authDataSource = authDataSource;

  /// Initialize token manager - call this after successful login
  Future<void> initialize() async {
    await _scheduleTokenRefresh();
  }
  /// Get current access token
  Future<String?> getAccessToken() async {
    return await Preferences.getAccessToken();
  }

  /// Get current refresh token
  Future<String?> getRefreshToken() async {
    return await Preferences.getRefreshToken();
  }

  /// Get token expiration time in seconds from now
  Future<int?> getTokenExpirationTime() async {
    final accessToken = await getAccessToken();
    if (accessToken == null) return null;
    
    try {
      final decodedToken = JwtDecoder.decode(accessToken);
      final exp = decodedToken['exp'] as int;
      final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      return exp - currentTime;
    } catch (e) {
      return null;
    }
  }

  /// Manually refresh token
  Future<bool> refreshToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) {
        _handleLogout();
        return false;
      }

      final response = await _authDataSource.refreshToken(
        refreshToken: refreshToken,
      );

      // Save new tokens
      await Preferences.saveTokens(
        accessToken: response.accessToken,
        refreshToken: response.refreshToken,
      );

      // Update user from new access token
      await Preferences.saveUserFromToken(response.accessToken);

      // Reschedule next refresh
      await _scheduleTokenRefresh();

      onTokenRefreshed?.call();
      return true;
    } catch (e) {
      onTokenRefreshError?.call(e.toString());
      _handleLogout();
      return false;
    }
  }

  /// Schedule automatic token refresh
  Future<void> _scheduleTokenRefresh() async {
    _refreshTimer?.cancel();
    
    final expirationTime = await getTokenExpirationTime();
    if (expirationTime == null || expirationTime <= 0) {
      _handleLogout();
      return;
    }

    // Schedule refresh 5 minutes before expiry
    final refreshTime = expirationTime - (_refreshBufferMinutes * 60);
    
    if (refreshTime <= 0) {
      // Token expires in less than 5 minutes, refresh immediately
      await refreshToken();
    } else {
      _refreshTimer = Timer(
        Duration(seconds: refreshTime),
        () async {
          await refreshToken();
        },
      );
    }
  }

  /// Handle logout (token expired or refresh failed)
  void _handleLogout() {
    onAutoLogout?.call();
    cleanup();
  }

  /// Clean up timers and clear tokens
  Future<void> cleanup() async {
    _refreshTimer?.cancel();
    _refreshTimer = null;

    // Clear stored tokens
    await Preferences.clearTokens();
  }

  /// Dispose of the token manager
  void dispose() {
    _refreshTimer?.cancel();
  }
}
