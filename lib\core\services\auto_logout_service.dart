import 'dart:async';
import 'package:flutter/material.dart';

/// Service that handles automatic logout after a period of user inactivity
class AutoLogoutService {
  Timer? _inactivityTimer;
  DateTime? _lastActivityTime;
  bool _isActive = false;
  
  // Default timeout is 1 hour (60 minutes)
  final Duration _inactivityTimeout;
  
  // Callback to be called when auto logout occurs
  final VoidCallback? onAutoLogout;

  AutoLogoutService({
    Duration? inactivityTimeout,
    this.onAutoLogout,
  }) : _inactivityTimeout = inactivityTimeout ?? const Duration(hours: 1);

  /// Start the auto logout service
  void start() {
    if (_isActive) return;
    
    _isActive = true;
    _updateLastActivity();
    _startInactivityTimer();
  }

  /// Stop the auto logout service
  void stop() {
    _isActive = false;
    _inactivityTimer?.cancel();
    _inactivityTimer = null;
    _lastActivityTime = null;
  }

  /// Update the last activity time and reset the inactivity timer
  /// Call this method whenever the user interacts with the app
  void updateActivity() {
    if (!_isActive) return;
    
    _updateLastActivity();
    _resetInactivityTimer();
  }

  /// Get the time since last activity
  Duration? get timeSinceLastActivity {
    if (_lastActivityTime == null) return null;
    return DateTime.now().difference(_lastActivityTime!);
  }

  /// Get the remaining time before auto logout
  Duration? get timeUntilAutoLogout {
    if (_lastActivityTime == null) return null;
    
    final timeSinceActivity = timeSinceLastActivity!;
    final remainingTime = _inactivityTimeout - timeSinceActivity;
    
    return remainingTime.isNegative ? Duration.zero : remainingTime;
  }

  /// Check if the service is currently active
  bool get isActive => _isActive;

  /// Get the configured inactivity timeout duration
  Duration get inactivityTimeout => _inactivityTimeout;

  /// Update the last activity timestamp
  void _updateLastActivity() {
    _lastActivityTime = DateTime.now();
  }

  /// Start the inactivity timer
  void _startInactivityTimer() {
    _inactivityTimer?.cancel();
    
    _inactivityTimer = Timer(_inactivityTimeout, () {
      if (_isActive) {
        _handleAutoLogout();
      }
    });
  }

  /// Reset the inactivity timer
  void _resetInactivityTimer() {
    _inactivityTimer?.cancel();
    _startInactivityTimer();
  }

  /// Handle the auto logout event
  void _handleAutoLogout() {
    stop();
    onAutoLogout?.call();
  }

  /// Dispose of the service
  void dispose() {
    stop();
  }
}

/// Extension to easily integrate auto logout with common Flutter widgets
extension AutoLogoutIntegration on AutoLogoutService {
  /// Create a GestureDetector that automatically updates activity
  /// Wrap your main app content with this to track user interactions
  Widget createActivityDetector({required Widget child}) {
    return GestureDetector(
      onTap: updateActivity,
      onPanStart: (_) => updateActivity(),
      onScaleStart: (_) => updateActivity(),
      behavior: HitTestBehavior.translucent,
      child: child,
    );
  }
}

/// Mixin to easily add auto logout functionality to any widget
mixin AutoLogoutMixin {
  AutoLogoutService? _autoLogoutService;

  /// Initialize auto logout for this widget
  void initAutoLogout({
    Duration? inactivityTimeout,
    VoidCallback? onAutoLogout,
  }) {
    _autoLogoutService = AutoLogoutService(
      inactivityTimeout: inactivityTimeout,
      onAutoLogout: onAutoLogout,
    );
    _autoLogoutService!.start();
  }

  /// Update activity - call this on user interactions
  void updateActivity() {
    _autoLogoutService?.updateActivity();
  }

  /// Dispose auto logout service
  void disposeAutoLogout() {
    _autoLogoutService?.dispose();
    _autoLogoutService = null;
  }

  /// Get the auto logout service instance
  AutoLogoutService? get autoLogoutService => _autoLogoutService;
}
