import 'dart:async';
import 'package:flutter/material.dart';

/// Service that handles automatic logout after a period of user inactivity
class AutoLogoutService {
  Timer? _inactivityTimer;
  DateTime? _lastActivityTime;
  bool _isActive = false;
  
  // Default timeout is 1 hour (60 minutes)
  final Duration _inactivityTimeout;
  
  // Callback to be called when auto logout occurs
  final VoidCallback? onAutoLogout;

  AutoLogoutService({
    Duration? inactivityTimeout,
    this.onAutoLogout,
  }) : _inactivityTimeout = inactivityTimeout ?? const Duration(hours: 1);

  /// Start the auto logout service
  void start() {
    if (_isActive) return;
    
    _isActive = true;
    _updateLastActivity();
    _startInactivityTimer();
  }

  /// Stop the auto logout service
  void stop() {
    _isActive = false;
    _inactivityTimer?.cancel();
    _inactivityTimer = null;
    _lastActivityTime = null;
  }

  /// Update the last activity timestamp
  void _updateLastActivity() {
    _lastActivityTime = DateTime.now();
  }

  /// Start the inactivity timer
  void _startInactivityTimer() {
    _inactivityTimer?.cancel();
    
    _inactivityTimer = Timer(_inactivityTimeout, () {
      if (_isActive) {
        _handleAutoLogout();
      }
    });
  }

  /// Handle the auto logout event
  void _handleAutoLogout() {
    stop();
    onAutoLogout?.call();
  }

  /// Dispose of the service
  void dispose() {
    stop();
  }
}