import 'dart:async';
// import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:wesell/core/utils/jwt_decoder.dart';
import '../utils/preferences.dart';
import 'token_manager.dart';

/// Service that handles automatic token refresh based on JWT expiration time
class AutoRefreshService {
  final TokenManager _tokenManager;
  Timer? _refreshTimer;
  bool _isActive = false;

  AutoRefreshService({required TokenManager tokenManager})
      : _tokenManager = tokenManager;

  /// Start the auto refresh service
  /// This should be called after successful login
  Future<void> start() async {
    if (_isActive) return;
    
    _isActive = true;
    await _scheduleNextRefresh();
  }

  /// Stop the auto refresh service
  void stop() {
    _isActive = false;
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Schedule the next token refresh based on current token expiration
  Future<void> _scheduleNextRefresh() async {
    if (!_isActive) return;

    _refreshTimer?.cancel();

    try {
      final accessToken = await Preferences.getAccessToken();
      if (accessToken == null) {
        stop();
        return;
      }

      // Check if token is already expired
      if (JwtDecoder.isExpired(accessToken)) {
        await _tokenManager.refreshToken();
        return;
      }

      // Get token expiration time
      final decodedToken = JwtDecoder.decode(accessToken);
      final exp = decodedToken['exp'] as int;
      final currentTimeInSeconds = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final timeUntilExpiry = exp - currentTimeInSeconds;

      // Schedule refresh 5 minutes (300 seconds) before expiry
      const refreshBufferSeconds = 300;
      final timeUntilRefresh = timeUntilExpiry - refreshBufferSeconds;

      if (timeUntilRefresh <= 0) {
        // Token expires in less than 5 minutes, refresh immediately
        await _performRefresh();
      } else {
        // Schedule refresh for the calculated time
        _refreshTimer = Timer(
          Duration(seconds: timeUntilRefresh),
          _performRefresh,
        );
      }
    } catch (e) {
      // If there's an error, try again in 1 minute
      _refreshTimer = Timer(
        const Duration(minutes: 1),
        () => _scheduleNextRefresh(),
      );
    }
  }

  /// Perform the actual token refresh
  Future<void> _performRefresh() async {
    if (!_isActive) return;

    try {
      final success = await _tokenManager.refreshToken();
      if (success) {
        // Schedule the next refresh based on the new token
        await _scheduleNextRefresh();
      } else {
        // Refresh failed, stop the service
        stop();
      }
    } catch (e) {
      // Refresh failed, try again in 1 minute
      if (_isActive) {
        _refreshTimer = Timer(
          const Duration(minutes: 1),
          _performRefresh,
        );
      }
    }
  }

  /// Check if the service is currently active
  bool get isActive => _isActive;

  /// Get the time until next refresh in seconds
  /// Returns null if no refresh is scheduled
  int? get timeUntilNextRefresh {
    if (_refreshTimer == null || !_refreshTimer!.isActive) return null;
    
    // This is an approximation since Timer doesn't expose remaining time
    // In a real implementation, you might want to track this more precisely
    return null;
  }

  /// Dispose of the service
  void dispose() {
    stop();
  }
}
