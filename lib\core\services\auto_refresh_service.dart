import 'dart:async';
import 'package:wesell/core/utils/jwt_decoder.dart';
import '../utils/preferences.dart';
import 'token_manager.dart';

/// Service that handles automatic token refresh based on JWT expiration time
class AutoRefreshService {
  final TokenManager _tokenManager;
  Timer? _refreshTimer;
  bool _isActive = false;

  AutoRefreshService({required TokenManager tokenManager})
      : _tokenManager = tokenManager;

  /// Start the auto refresh service
  /// This should be called after successful login
  Future<void> start() async {
    if (_isActive) return;
    
    _isActive = true;
    await _scheduleNextRefresh();
  }

  /// Stop the auto refresh service
  void stop() {
    _isActive = false;
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Schedule the next token refresh based on current token expiration
  Future<void> _scheduleNextRefresh() async {
    if (!_isActive) return;

    _refreshTimer?.cancel();

    try {
      final accessToken = await Preferences.getAccessToken();
      if (accessToken == null) {
        stop();
        return;
      }

      // Get token expiration time
      final decodedToken = JwtDecoder.decode(accessToken);

      // Check if token is already expired
      final exp = decodedToken['exp'] as int;
      final currentTimeInSeconds = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      if (exp <= currentTimeInSeconds) {
        await _tokenManager.refreshToken();
        return;
      }

      final timeUntilExpiry = exp - currentTimeInSeconds;

      // Schedule refresh 5 minutes (300 seconds) before expiry
      const refreshBufferSeconds = 300;
      final timeUntilRefresh = timeUntilExpiry - refreshBufferSeconds;

      if (timeUntilRefresh <= 0) {
        // Token expires in less than 5 minutes, refresh immediately
        await _performRefresh();
      } else {
        // Schedule refresh for the calculated time
        _refreshTimer = Timer(
          Duration(seconds: timeUntilRefresh),
          _performRefresh,
        );
      }
    } catch (e) {
      // If there's an error, try again in 1 minute
      _refreshTimer = Timer(
        const Duration(minutes: 1),
        () => _scheduleNextRefresh(),
      );
    }
  }

  /// Perform the actual token refresh
  Future<void> _performRefresh() async {
    if (!_isActive) return;

    try {
      final success = await _tokenManager.refreshToken();
      if (success) {
        // Schedule the next refresh based on the new token
        await _scheduleNextRefresh();
      } else {
        // Refresh failed, stop the service
        stop();
      }
    } catch (e) {
      // Refresh failed, try again in 1 minute
      if (_isActive) {
        _refreshTimer = Timer(
          const Duration(minutes: 1),
          _performRefresh,
        );
      }
    }
  }
}
