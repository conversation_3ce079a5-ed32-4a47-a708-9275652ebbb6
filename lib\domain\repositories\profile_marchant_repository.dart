import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../data/models/profile_marchant_model.dart';
import '../../data/models/update_company_details_response.dart';

abstract class ProfileMarchantRepository {
  Future<Either<Failure, ProfileMarchantModel>> getProfile();
  Future<Either<Failure, UpdateCompanyDetailsResponse>> updateCompanyDetails({
    required String companyName,
    required String logo,
    required String description,
    required String detailedDescription,
  });
  Future<Either<Failure, UpdateCompanyDetailsResponse>> updateIndustryDetails({
    required List<String> industries,
  });
  Future<Either<Failure, UpdateCompanyDetailsResponse>> updateCompanyLocations({
    required String country,
    required String city,
    required String address,
    required String poBox,
  });
  Future<Either<Failure, UpdateCompanyDetailsResponse>> updateDigitalInformation({
    required String website,
    required String facebook,
    required String twitter,
    required String linkedin,
    required String youtube,
    required String tiktok,
    required String snapchat,
  });
  Future<Either<Failure, UpdateCompanyDetailsResponse>> updateLegalDocuments({
    required String companyLegalName,
    required String companySize,
    required String crNumber,
    required int investedCapital,
    required String investedCapitalUnit,
     String? crDocument,
     String? licenseDocument,
  });

  Future<Either<Failure, Map<String, dynamic>>> getFileMetadata(String fileId);
}
