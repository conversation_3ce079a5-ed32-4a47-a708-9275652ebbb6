import 'package:flutter/material.dart';
import 'package:wesell/core/constants/app_colors.dart';
import 'package:wesell/core/constants/img_string.dart';
import 'package:wesell/core/utils/preferences.dart';
import 'package:wesell/data/models/user_model.dart';
import 'package:wesell/presentation/widgets/custom_svg_widget.dart';
import 'package:wesell/presentation/widgets/dashboard_card.dart';
import '../../../core/localization/app_localizations.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    User data = await Preferences.getUser() as User;
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundTheme,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: AppColors.whiteTheme,
        title: Text(localizations.home),
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: counterCard(),
    );
  }

  Widget counterCard(){
    return Padding(
        padding: const EdgeInsets.all(8),
        child: LayoutBuilder(
          builder: (context, constraints) {
            return GridView.count(
              crossAxisCount: 2,
              childAspectRatio: 1.05,
              children: const [
                DashboardStatCard(
                  value: '8,500',
                  label: 'SAR',
                  subtitle: 'Total',
                  icon: ImageStrings.saru,
                  iconBgColor: AppColors.bageGreen,
                ),
                DashboardStatCard(
                  value: '769',
                  label: 'Products',
                  subtitle: 'Total Sold Products',
                  icon: ImageStrings.product,
                  iconBgColor: AppColors.bagePink,
                ),
                DashboardStatCard(
                  value: '20',
                  label: 'Jobs',
                  subtitle: 'Total Posted Jobs',
                  icon: ImageStrings.jobsGrid,
                  iconBgColor: AppColors.bageBlue,
                ),
                DashboardStatCard(
                  value: '305',
                  label: 'Sellers',
                  subtitle: 'Total Sellers',
                  icon: ImageStrings.sellerGrid,
                  iconBgColor: AppColors.bagePurple,
                ),
                DashboardStatCard(
                  value: '1,249',
                  label: 'Buyers',
                  subtitle: 'Total Buyers',
                  icon: ImageStrings.buyer,
                  iconBgColor: AppColors.bageTeal,
                ),
                DashboardStatCard(
                  value: '2,000',
                  label: 'SAR',
                  subtitle: 'Total Expenses',
                  icon: ImageStrings.sar,
                  iconBgColor: AppColors.bageRed,
                ),
              ],
            );
          },
        ),
      );
  }
}